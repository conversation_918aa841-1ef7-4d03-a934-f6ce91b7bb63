import request from '@/utils/request'

// 调度方案-列表分页查询
export function getSchemaPage(data) {
  return request({
    url: '/model/schema/page',
    method: 'post',
    data,
  })
}

// 调度方案-删除
export function deleteSchema(params) {
  return request({
    url: '/model/schema/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 调度方案-新增
export function addSchema(data) {
  return request({
    url: '/model/schema/add',
    method: 'post',
    data,
  })
}

// 调度方案-更新
export function updateSchema(data) {
  return request({
    url: '/model/schema/instruct/update',
    method: 'post',
    data,
  })
}

// 调度方案-详情
export function getSchema(params) {
  return request({
    url: '/model/schema/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//
// 生成配水文案——下发
export function issuedSchema(data) {
  return request({
    url: '/model/schema/send',
    method: 'post',
    data,
  })
}
// 获取闸门实时监测

export function getLastProjectList() {
  return request({
    url: '/model/schema/getLastProjectList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 配水方案列表
export function getWaterScheme(data) {
  return request({
    url: '/model/water/scheme/page',
    method: 'post',
    data,
  })
}
//用水计划列表
export function getReportList(params) {
  return request({
    url: '/model/plan/report/getReportList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取评价方式
export function getSchemaEvaluates(params) {
  return request({
    url: '/model/schema/getEvaluates',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
