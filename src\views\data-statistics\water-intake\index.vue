<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="灌溉时间">
        <a-range-picker
          allow-clear
          style="width: 100%"
          v-model="rangeDate"
          :placeholder="['开始日期', '结束日期']"
          :disabled-date="disabledDate"
          @change="onDateRangeChange"
          @openChange="onOpenChange"
          @calendarChange="onCalendarChange"
        >
          <template slot="dateRender" slot-scope="current">
            <div class="ant-calendar-date" :style="getDateCellStyle(current)">
              {{ current.date() }}
            </div>
          </template>
        </a-range-picker>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :border="true" 
          :columns="columns"
          :tableData="tableData"
          :tableTitle="tableTitle"
          :loading="loading"
          :isAdaptPageSize="false"
          :tablePage="false"
          :mergeCells="mergeCells"
          :show-footer="false"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleImport()" icon="download" :loading="exportLoading">导入</a-button>
            <a-button type="primary" @click="handleExport()" icon="download" :loading="exportLoading">导出</a-button>
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import { getProjectPage } from '@/api/common'
  import { getRoundByDate, getWaterIntake, getDeptTree, getIrrigationRound } from './services'
  import moment from 'moment'

  export default {
    name: 'WaterIntake',
    components: {
      VxeTableForm,
      VxeTable,
    },
    data() {
      return {
        rangeDate: [],
        list: [],
        loading: false,
        exportLoading: false,
        deptList: [], // 部门列表
        roundId: null, // 轮次ID
        roundName: '', // 轮次名称（春夏灌等）
        endDateText: '', // 截止日期文本
        customDateRange: null, // 自定义日期区间
        isSelectingDate: false, // 是否正在选择日期

        tableTitle: '',
        yjDitchList: [],

        queryParam: {
          lineId: undefined,
          pageNum: 1,
          pageSize: 10,
          planEndTime: '',
          planStartTime: '',
          sort: [],
          taskName: '',
          taskStatus: undefined,
        },

        // 固定的表格结构定义
        fixedTableStructure: [
          {
            id: 'nanbian',
            yjName: '永济灌域',
            deptName: '南边',
            subDeptName: '',
            depId: 10021,
            level: 1,
            parentId: null
          },
          {
            id: 'beibei',
            yjName: '',
            deptName: '北边',
            subDeptName: '',
            depId: 10022,
            level: 1,
            parentId: 'nanbian'
          },
          {
            id: 'heji',
            yjName: '',
            deptName: '合济',
            subDeptName: '',
            depId: 10035,
            level: 1,
            parentId: 'nanbian'
          },
          {
            id: 'yongjiqu1',
            yjName: '',
            deptName: '永济渠',
            subDeptName: '',
            depId: 10070,
            level: 1,
            parentId: 'nanbian',
          },
          {
            id: 'total',
            yjName: '',
            deptName: '全灌域合计',
            subDeptName: '',
            depId: null,
            level: 1,
            parentId: 'nanbian',
            isTotal: true
          },
          {
            id: 'yongjiqu',
            yjName: '',
            deptName: '永济渠',
            subDeptName: '永兰',
            depId: 10036,
            level: 1,
            parentId: 'nanbian',
            hasChildren: true
          },
          {
            id: 'yonggang',
            yjName: '',
            deptName: '',
            subDeptName: '永刚',
            depId: 10037,
            level: 2,
            parentId: 'yongjiqu'
          },
          {
            id: 'xinhua',
            yjName: '',
            deptName: '',
            subDeptName: '新华',
            depId: 10039,
            level: 2,
            parentId: 'yongjiqu'
          },
          {
            id: 'xile',
            yjName: '',
            deptName: '',
            subDeptName: '西乐',
            depId: 10038,
            level: 2,
            parentId: 'yongjiqu'
          },
          {
            id: 'zhengshao',
            yjName: '',
            deptName: '',
            subDeptName: '正稍',
            depId: 10040,
            level: 2,
            parentId: 'yongjiqu'
          },
          {
            id: 'ganqu',
            yjName: '',
            deptName: '',
            subDeptName: '干渠',
            depId: 10041,
            level: 2,
            parentId: 'yongjiqu'
          },
          {
            id: 'yongjiqu_total',
            yjName: '',
            deptName: '永济渠合计',
            subDeptName: '',
            depId: null,
            level: 2,
            parentId: 'yongjiqu',
            isYongjiTotal: true
          },
          {
            id: 'remark',
            yjName: '',
            deptName: '',
            subDeptName: '',
            depId: null,
            level: 1,
            parentId: null,
            isRemark: true
          }
        ],

        columns: [
          {
            title: '所名',
            field: 'yjName',
            align: 'center',
            headerAlign: 'center',
            width: 100,
            fixed: 'left',
            slots: {
              header: ({ column }) => {
                return (
                  <div class='first-col'>
                    <div class='first-col-top'>项目</div>
                    <div class='first-col-bottom'>所名</div>
                  </div>
                )
              },
            },
          },
          {
            title: '',
            field: 'deptName',
            align: 'center',
            headerAlign: 'center',
            width: 120, // 增加宽度，原来是80
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
          },
          {
            title: '',
            field: 'subDeptName',
            align: 'center',
            headerAlign: 'center',
            width: 80,
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
          },
          {
            title: '口部水量（流量日）',
            field: 'group1',
            align: 'center',
            headerAlign: 'center',
            children: [
              { field: 'waterAmount', title: '实引水量', align: 'center', headerAlign: 'center' },
              { field: 'planWaterAmount', title: '分配计划水量', align: 'center', headerAlign: 'center' },
              { field: 'surplusWaterAmount', title: '剩余水量', align: 'center', headerAlign: 'center' },
            ],
          },
          {
            title: '直口水量（流量日）',
            field: 'group2',
            align: 'center',
            headerAlign: 'center',
            children: [
              { field: 'directWaterAmount', title: '实引水量', align: 'center', headerAlign: 'center' },
              { field: 'directPlanWaterAmount', title: '指标水量', align: 'center', headerAlign: 'center' },
              { field: 'directSurplusWaterAmount', title: '剩余水量', align: 'center', headerAlign: 'center' },
            ],
          },
          { field: 'totalPourAmount', title: '浇地面积', align: 'center', headerAlign: 'center' },
          { field: 'pourEfficiency', title: '灌溉效率', align: 'center', headerAlign: 'center' },
          { field: 'irrigationQuota', title: '灌溉定额', align: 'center', headerAlign: 'center' },
          { field: 'coefficient', title: '系数', align: 'center', headerAlign: 'center' },
        ],
        tableData: [],
        footerData: [],
        remark: '', // 备注
        mergeCells: [], // 合并单元格配置
      }
    },
    computed: {},
    watch: {},
    created() {
      this.initData()
      // 自动获取当前日期并查询当前轮次
      const currentDate = moment().format('YYYY-MM-DD')
      this.rangeDate = [moment().subtract(30, 'days'), moment()]
      this.getRoundByEndDate(moment(), false) // 添加参数false，表示不自动获取水量统计数据
    },
    methods: {
      // 禁用今天以后的日期
      disabledDate(current) {
        // 禁用今天以后的日期
        return current && current > moment().endOf('day')
      },

      // 日期选择器打开/关闭事件
      onOpenChange(status) {
        if (!status) {
          // 关闭时重置选择状态
          this.isSelectingDate = false
        }
      },

      // 日历变化事件（当用户点击日期时触发）
      async onCalendarChange(dates) {
        if (!dates || dates.length === 0) return

        // 当用户选择了一个日期时
        if (dates.length === 1 || (dates.length === 2 && dates[1])) {
          this.isSelectingDate = true
          const selectedDate = dates[dates.length - 1] // 获取最后选择的日期

          try {
            // 调用getRoundByDate接口
            const response = await getRoundByDate({
              date: moment(selectedDate).format('YYYY-MM-DD')
            })

            if (response.success && response.data) {
              const { startDate, endDate } = response.data
 
              if (startDate && endDate) {
                // 设置自定义日期区间用于样式显示
                this.customDateRange = {
                  start: moment(startDate),
                  end: moment(endDate)
                }

                // 更新日期选择器的值
                this.rangeDate = [moment(startDate), moment(endDate)]
              } else {
                // 清空自定义区间
                this.customDateRange = null
                this.$message.warning('该日期没有灌溉进度填报数据，请重新选择')
              }
            } else {
              // 清空自定义区间
              this.customDateRange = null
              this.$message.warning('该日期没有灌溉进度填报数据，请重新选择')
            }
          } catch (error) {
            console.error('获取日期区间失败:', error)
            this.customDateRange = null
            this.$message.error('获取日期区间失败，请重新选择')
          }
        }
      },

      // 获取日期单元格样式
      getDateCellStyle(current) {
        const style = {}

        // 如果有自定义日期区间，用圆圈标记区间内的日期
        if (this.customDateRange) {
          const { start, end } = this.customDateRange
          if (current.isSameOrAfter(start, 'day') && current.isSameOrBefore(end, 'day')) {
            if (current.isSame(start, 'day') && current.isSame(end, 'day')) {
              // 单日
              style.backgroundColor = '#1890ff'
              style.color = '#fff'
              style.borderRadius = '4px'
              // style.borderRadius = '50%'
              // style.width = '24px'
              // style.height = '24px'
              // style.display = 'flex'
              // style.alignItems = 'center'
              // style.justifyContent = 'center'
              // style.margin = '0 auto'
            } else if (current.isSame(start, 'day')) {
              // 开始日期
              style.backgroundColor = '#1890ff'
              style.color = '#fff'
              style.borderRadius = '4px 0 0 4px'
              // style.borderRadius = '50%'
              // style.width = '24px'
              // style.height = '24px'
              // style.display = 'flex'
              // style.alignItems = 'center'
              // style.justifyContent = 'center'
              // style.margin = '0 auto'
            } else if (current.isSame(end, 'day')) {
              // 结束日期
              style.backgroundColor = '#1890ff'
              style.color = '#fff'
              style.borderRadius = '0 4px 4px 0'
              // style.borderRadius = '50%'
              // style.width = '24px'
              // style.height = '24px'
              // style.display = 'flex'
              // style.alignItems = 'center'
              // style.justifyContent = 'center'
              // style.margin = '0 auto'
            } else {
              // 区间内的日期
              style.backgroundColor = '#e6f7ff'
              // style.border = '2px solid #1890ff'
              style.color = '#1890ff'
              // style.borderRadius = '50%'
              // style.width = '24px'
              // style.height = '24px'
              // style.display = 'flex'
              // style.alignItems = 'center'
              // style.justifyContent = 'center'
              // style.margin = '0 auto'
              // style.backgroundColor = 'transparent'
            }
          }
        }

        return style
      },

      // 时间范围变化处理
      onDateRangeChange(dates) {
        console.log('时间范围变化:', dates)
        if (dates && dates.length === 2 && dates[1]) {
          // 当选择了结束时间时，调用getRoundByDate接口
          this.getRoundByEndDate(dates[1], false) // 添加参数false，表示不自动获取水量统计数据
        } else {
          // 清空时间时，重置数据
          this.roundId = null
          this.roundName = ''
          this.endDateText = ''
          this.remark = '' // 清空备注
          this.customDateRange = null // 清空自定义区间
          this.updateTableTitle()
          this.initializeTableData()
        }
      },

      // 初始化表格数据
      initializeTableData() {
        this.tableData = this.fixedTableStructure.map(item => {
          if (item.isRemark) {
            return {
              ...item,
              yjName: '备注',
              deptName: '',
              subDeptName: '',
              waterAmount: this.remark,
              planWaterAmount: '',
              surplusWaterAmount: '',
              directWaterAmount: '',
              directPlanWaterAmount: '',
              directSurplusWaterAmount: '',
              totalPourAmount: '',
              pourEfficiency: '',
              irrigationQuota: '',
              coefficient: ''
            }
          }
          return {
            ...item,
            subDeptName: item.subDeptName || (item.level === 2 ? item.deptName : ''), // 二级项目显示在第三列
            waterAmount: '',
            planWaterAmount: '',
            surplusWaterAmount: '',
            directWaterAmount: '',
            directPlanWaterAmount: '',
            directSurplusWaterAmount: '',
            totalPourAmount: '',
            pourEfficiency: '',
            irrigationQuota: '',
            coefficient: ''
          }
        })
        
        this.footerData = []
        
        this.calculateMergeCells()
      },

      // 初始化数据
      async initData() {
        try {
          // 获取项目信息
          const projectRes = await getProjectPage({ 
            districtCode: '0', 
            pageNum: 1, 
            pageSize: Number.MAX_SAFE_INTEGER 
          })
          this.yjDitchList = projectRes?.data?.data || []
          
          // 获取部门树
          await this.loadDeptData()
          
          // 初始化表格数据
          this.initializeTableData()
          
          this.getList()
        } catch (error) {
          console.error('初始化数据失败:', error)
        }
      },

      // 获取部门数据
      async loadDeptData() {
        try {
          const response = await getDeptTree()
          if (response.success && response.data && response.data.length > 0) {
            // 获取第一个children内的数组（各供水所）
            const firstLevel = response.data[0]
            if (firstLevel && firstLevel.children) {
              this.deptList = firstLevel.children
            }
          }
        } catch (error) {
          console.error('加载部门数据失败:', error)
        }
      },

      // 通过结束时间获取轮次
      async getRoundByEndDate(endDate, autoLoadData = true) {
        try {
          const endDateFormatted = moment(endDate).format('YYYY-MM-DD')
          console.log('调用getRoundByDate接口，结束日期:', endDateFormatted)
          
          // 获取截止日期文本（如：5月25日）
          this.endDateText = moment(endDate).format('M月D日')
          
          // 获取开始日期，如果没有选择开始日期，则默认使用结束日期减去30天
          let startDateFormatted = ''
          if (this.rangeDate && this.rangeDate[0]) {
            startDateFormatted = moment(this.rangeDate[0]).format('YYYY-MM-DD')
          } else {
            startDateFormatted = moment(endDate).subtract(30, 'days').format('YYYY-MM-DD')
          }
          
          // 获取轮次信息，同时传递开始日期和结束日期
          const roundResponse = await getIrrigationRound(endDateFormatted, startDateFormatted)
          console.log('getIrrigationRound响应:', roundResponse)
          
          // 如果成功获取轮次信息，设置轮次名称
          if (roundResponse && roundResponse.success && roundResponse.data) {
            // 根据irrigationRound值确定轮次名称
            const irrigationRound = roundResponse.data.irrigationRound
            if (irrigationRound === 1) {
              this.roundName = '春夏灌'
            } else if (irrigationRound === 2) {
              this.roundName = '秋灌'
            } else if (irrigationRound === 3) {
              this.roundName = '冬灌'
            } else {
              this.roundName = ''
            }
            console.log('设置轮次名称:', this.roundName)
          }
          
          // 更新表格标题
          this.updateTableTitle()
          
          const response = await getRoundByDate({
            date: endDateFormatted
          })
          
          console.log('getRoundByDate响应:', response)
          
          if (response.success && response.data) {
            const { endDate: resEndDate, startDate, id } = response.data
            
            console.log('获取到轮次数据:', { resEndDate, startDate, id })
            
            // 如果返回的开始时间和结束时间不为空，则更新时间选择器
            if (resEndDate && startDate) {
              // 避免无限循环，只在时间不同时才更新
              const newStartDate = moment(startDate)
              const newEndDate = moment(resEndDate)
              
              console.log('更新时间选择器:', { newStartDate: newStartDate.format(), newEndDate: newEndDate.format() })
              
              if (!this.rangeDate ||  
                  !this.rangeDate[0] || 
                  !this.rangeDate[1] ||
                  !newStartDate.isSame(this.rangeDate[0]) ||
                  !newEndDate.isSame(this.rangeDate[1])) {
                this.rangeDate = [newStartDate, newEndDate]
              }
            }
            
            // 保存轮次ID
            this.roundId = id
            
            // 如果有轮次ID且需要自动加载数据，则调用获取水量统计数据的接口
            if (id && autoLoadData) {
              this.getWaterIntakeData(id)
            }
          } else {
            // 如果返回数据为null，清空轮次ID和备注
            this.roundId = null
            this.remark = '' // 清空备注
            
            // 如果需要自动加载数据，则初始化空表格
            if (autoLoadData) {
              this.initializeTableData()
            }
          }
        } catch (error) {
          console.error('获取轮次失败:', error)
          this.$message.error('获取轮次信息失败')
          this.roundId = null
        }
      },

      // 获取水量统计数据
      async getWaterIntakeData(roundId) {
        try {
          this.loading = true
          console.log('调用getWaterIntake接口，轮次ID:', roundId)
          
          const response = await getWaterIntake({ id: roundId })
          
          console.log('getWaterIntake响应:', response)
          
          if (response.success && response.data) {
            const { remark, statsList } = response.data
            
            console.log('获取到统计数据:', { remark, statsList })
            
            // 设置备注
            this.remark = remark || ''
            
            // 检查是否有统计数据
            if (!statsList || statsList.length === 0) {
              this.$message.info('暂无数据')
              // 初始化空表格
              this.initializeTableData()
              return
            }
            
            // 处理统计数据
            this.processWaterIntakeData(statsList || [])
            
            // 更新备注行数据
            const remarkRow = this.tableData.find(row => row.isRemark)
            if (remarkRow) {
              remarkRow.yjName = '备注'
              remarkRow.waterAmount = this.remark
            }
            
            // 设置表格底部数据
            this.footerData = []
            
            console.log('最终表格数据:', this.tableData)
            console.log('底部数据:', this.footerData)
            
            // 计算合并单元格
            this.calculateMergeCells()
          } else {
            this.$message.info('暂无数据')
            // 初始化空表格
            this.initializeTableData()
          }
        } catch (error) {
          console.error('获取水量统计数据失败:', error)
          this.$message.error('获取水量统计数据失败')
          // 初始化空表格
          this.initializeTableData()
        } finally {
          this.loading = false
        }
      },

      // 处理水量统计数据
      processWaterIntakeData(statsList) {
        // 先初始化固定结构的表格数据
        this.tableData = this.fixedTableStructure.map(item => {
          if (item.isRemark) {
            return {
              ...item,
              yjName: '备注',
              deptName: '',
              subDeptName: '',
              waterAmount: this.remark,
              planWaterAmount: '',
              surplusWaterAmount: '',
              directWaterAmount: '',
              directPlanWaterAmount: '',
              directSurplusWaterAmount: '',
              totalPourAmount: '',
              pourEfficiency: '',
              irrigationQuota: '',
              coefficient: ''
            }
          }
          return {
            ...item,
            subDeptName: item.subDeptName || (item.level === 2 ? item.deptName : ''), // 二级项目显示在第三列
            waterAmount: '',
            planWaterAmount: '',
            surplusWaterAmount: '',
            directWaterAmount: '',
            directPlanWaterAmount: '',
            directSurplusWaterAmount: '',
            totalPourAmount: '',
            pourEfficiency: '',
            irrigationQuota: '',
            coefficient: ''
          }
        })

        // 创建一个depId到数据的映射
        const dataMap = {}
        statsList.forEach(item => {
          if (item.depId) {
            dataMap[item.depId] = item
          }
        })

        // 检查是否获取到数据
        if (statsList.length === 0) {
          // 如果没有获取到数据，显示"暂无数据"
          this.$message.info('暂无数据')
          return
        }

        // 将API返回的数据填充到对应的行
        this.tableData.forEach(row => {
          if (row.depId && dataMap[row.depId]) {
            const apiData = dataMap[row.depId]
            // 填充数据字段
            Object.keys(apiData).forEach(key => {
              if (key !== 'depId') {
                // 对数值类型的字段进行处理，保留两位小数
                if (typeof apiData[key] === 'number') {
                  row[key] = apiData[key].toFixed(2)
                } else {
                  // 保留原值，包括空值、null或undefined
                  row[key] = apiData[key]
                }
              }
            })
          }
        })

        // 计算全灌域合计
        this.calculateTotalData()
      },

      // 计算全灌域合计数据
      calculateTotalData() {
        const totalRow = this.tableData.find(row => row.isTotal)
        const yongjiTotalRow = this.tableData.find(row => row.isYongjiTotal)
        
        // 计算合计值的字段
        const fields = [
          'waterAmount', 'planWaterAmount', 'surplusWaterAmount',
          'directWaterAmount', 'directPlanWaterAmount', 'directSurplusWaterAmount',
          'totalPourAmount', 'pourEfficiency', 'irrigationQuota', 'coefficient'
        ]

        // 计算永济渠合计
        if (yongjiTotalRow) {
          // 永济渠的子项（永兰、永刚、新华、西乐、正稍、干渠）
          const yongjiRows = this.tableData.filter(row => 
            [10036, 10037, 10039, 10038, 10040, 10041].includes(row.depId)
          )

          fields.forEach(field => {
            const sum = yongjiRows.reduce((sum, row) => {
              return sum + (parseFloat(row[field]) || 0)
            }, 0)
            yongjiTotalRow[field] = sum.toFixed(2)
          })
        }

        // 计算全灌域合计
        if (totalRow) {
          // 找到南边、北边、合济、永济渠这四行数据
          const nanBianRow = this.tableData.find(row => row.id === 'nanbian')
          const beiBianRow = this.tableData.find(row => row.id === 'beibei')
          const heJiRow = this.tableData.find(row => row.id === 'heji')
          const yongjiqu1Row = this.tableData.find(row => row.id === 'yongjiqu1')
          
          // 需要参与合计的四行
          const summaryRows = [nanBianRow, beiBianRow, heJiRow, yongjiqu1Row].filter(row => row !== undefined)

          fields.forEach(field => {
            const sum = summaryRows.reduce((sum, row) => {
              return sum + (parseFloat(row[field]) || 0)
            }, 0)
            totalRow[field] = sum.toFixed(2)
          })
        }
      },

      // 计算合并单元格配置
      calculateMergeCells() {
        // 找到备注行的索引
        const remarkIndex = this.tableData.findIndex(row => row.isRemark)
        const dataRowCount = remarkIndex >= 0 ? remarkIndex : this.tableData.length - 1

        this.mergeCells = [
          // 第一列（永济灌域）合并所有数据行（不包括备注行）
          { row: 0, col: 0, rowspan: dataRowCount, colspan: 1 },
          // 永济渠行在第二列合并到其所有子项（包括永济渠合计）
          { row: 5, col: 1, rowspan: 6, colspan: 1 }
        ]

        // 找到需要向右合并单元格的行索引
        const nanBianIndex = this.tableData.findIndex(row => row.id === 'nanbian')
        const beiBianIndex = this.tableData.findIndex(row => row.id === 'beibei')
        const heJiIndex = this.tableData.findIndex(row => row.id === 'heji')
        const totalIndex = this.tableData.findIndex(row => row.id === 'total')
        const yongjiqu1Index = this.tableData.findIndex(row => row.id === 'yongjiqu1')
        const yongjiTotalIndex = this.tableData.findIndex(row => row.id === 'yongjiqu_total')

        // 向右合并单元格（将第二列和第三列合并）
        if (nanBianIndex >= 0) {
          this.mergeCells.push({ row: nanBianIndex, col: 1, rowspan: 1, colspan: 2 })
        }

        if (beiBianIndex >= 0) {
          this.mergeCells.push({ row: beiBianIndex, col: 1, rowspan: 1, colspan: 2 })
        }

        if (heJiIndex >= 0) {
          this.mergeCells.push({ row: heJiIndex, col: 1, rowspan: 1, colspan: 2 })
        }

        if (totalIndex >= 0) {
          this.mergeCells.push({ row: totalIndex, col: 1, rowspan: 1, colspan: 2 })
        }

        if (yongjiqu1Index >= 0) {
          this.mergeCells.push({ row: yongjiqu1Index, col: 1, rowspan: 1, colspan: 2 })
        }

        // 永济渠合计行向左合并一个单元格（将第二列和第三列合并）
        if (yongjiTotalIndex >= 0) {
          this.mergeCells.push({ row: yongjiTotalIndex, col: 1, rowspan: 1, colspan: 2 })
        }

        // 如果有备注行，合并备注行的前三列作为备注标题，后面的列作为备注内容
        if (remarkIndex >= 0) {
          this.mergeCells.push(
            // 前三列合并显示"备注"
            { row: remarkIndex, col: 0, rowspan: 1, colspan: 3 },
            // 后面的列合并显示备注内容
            { row: remarkIndex, col: 3, rowspan: 1, colspan: 10 }
          )
        }
      },

      // 更新表格标题
      updateTableTitle() {
        const roundText = this.roundName ? `（${this.roundName}）` : ''
        const endDateText = this.endDateText ? `（截止${this.endDateText}）` : ''
        this.tableTitle = `永济灌域${roundText}干口及直口实引水量统计表${endDateText}`
      },

      /** 查询列表 */
      getList() {
        this.updateTableTitle()
        // 如果有轮次ID，重新获取数据，否则初始化空表格
        if (this.roundId) {
          this.getWaterIntakeData(this.roundId)
        } else {
          // 当轮次ID为null时，初始化空表格
          this.initializeTableData()
        }
      },
      
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      
      /** 重置按钮操作 */
      resetQuery() {
        this.rangeDate = []
        this.roundId = null
        this.roundName = ''
        this.endDateText = ''
        this.customDateRange = null // 清空自定义日期区间
        this.isSelectingDate = false // 重置选择状态
        this.queryParam = {
          ...this.queryParam,
          lineId: undefined,
          pageNum: 1,
          planEndTime: '',
          planStartTime: '',
          taskName: '',
          taskStatus: undefined,
        }
        this.remark = '' // 确保清空备注
        this.updateTableTitle()
        this.initializeTableData()
        this.handleQuery()
      },

      handleImport() {},
      handleExport() {},
    },
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }

  // 为表格添加底部距离
  ::v-deep(.vxe-table-box) {
    margin-bottom: 30px;
  }

  ::v-deep(.header-bar) {
    .title {
      text-align: center;
      width: 100%;
      position: absolute;
    }
  }
  ::v-deep(.vxe-table--header) {
    .first-col {
      position: relative;
      height: 20px;
      &:before {
        content: '';
        position: absolute;
        left: -32px;
        top: 6px;
        width: 138px;
        height: 1px;
        transform: rotate(42deg);
        background-color: #e8eaec;
      }
      .first-col-top {
        position: absolute;
        right: 4px;
        top: -10px;
      }
      .first-col-bottom {
        position: absolute;
        left: 4px;
        bottom: -10px;
      }
    }
  }
  // 样式补充
  ::v-deep(.vxe-header--column),
  ::v-deep(.vxe-cell) {
    text-align: center;
  }

  // 隐藏表头单元格样式
  ::v-deep(.hidden-cell) {
    visibility: hidden;
  }
</style>
