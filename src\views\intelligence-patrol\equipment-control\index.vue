<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <div class="top">
      <div class="query-box" v-if="viewType === '1'">
        <span class="name">关键字：</span>
        <a-select
          class="query-input"
          allowClear
          show-search
          :filter-option="filterOption"
          v-model="queryParam.cameraName"
          :options="cameraOptions"
          placeholder="请输入"
          @change="onChangeCamera"
        ></a-select>
        <a-button @click="handleQuery">
          <a-icon type="search" />
          查询
        </a-button>
        <a-button @click="resetQuery">
          <a-icon type="reload" />
          重置
        </a-button>
      </div>
      <a-radio-group class="query-radio-group" v-model="viewType" button-style="solid" @change="onTabChange">
        <a-radio-button value="1">地图模式</a-radio-button>
        <a-radio-button value="2">表格模式</a-radio-button>
      </a-radio-group>
    </div>
    <CameraMap :list="cameraOptions" :queryCameraId="queryCameraId" v-if="viewType === '1'" />
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery" v-if="viewType == '2'">
      <a-form-item label="类型">
        <a-select
          allowClear
          v-model="queryParam.cameraType"
          :options="cameraTypeOptions"
          placeholder="请选择"
        ></a-select>
      </a-form-item>

      <a-form-model-item label="所属工程">
        <a-tree-select
          v-model="queryParam.treeNodeId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'key',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-model-item>
      <a-form-item label="">
        <a-input v-model="queryParam.cameraName" placeholder="请输入关键字" />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="设备管控"
          :columns="columns"
          :tableData="cameraOptions"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
        ></VxeTable>
      </template>
    </VxeTableForm>

    <CameraPlay v-if="showCameraPlay" ref="cameraPlayRef" @close="showCameraPlay = false" />
  </div>
</template>

<script lang="jsx">
  import { getProjectCategoryTree } from '@/api/common'
  import { getCameraPage, deleteCamera } from '@/views/basic/camera/services.js'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import CameraMap from './components/CameraMap.vue'
  import CameraPlay from './modules/CameraPlay.vue'

  export default {
    name: 'EquipmentControl',
    components: {
      VxeTableForm,
      VxeTable,
      CameraMap,
      CameraPlay,
    },
    data() {
      return {
        loading: false,
        viewType: '1',
        projectOptions: [],
        cameraOptions: [],
        total: 0,
        showCameraPlay: false,
        queryCameraId: undefined,

        queryParam: {
          cameraCode: '',
          cameraName: undefined,
          cameraType: undefined,
          districtCode: '',
          otherTreeNodeId: undefined,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          riverTreeNodeId: undefined,
          siteTreeNodeId: undefined,
          sort: [],
          treeNodeId: undefined,
          treeNodeType: 'data',
        },

        cameraTypeOptions: [
          { label: '枪机', value: 1 },
          { label: '球机', value: 2 },
        ],
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '视频点编号',
            field: 'cameraCode',
            minWidth: 120,
          },
          {
            title: '视频点名称',
            field: 'cameraName',
            minWidth: 120,
          },
          {
            title: '设备号',
            field: 'deviceCode',
            minWidth: 120,
          },
          {
            title: '通道号',
            field: 'channelCode',
            minWidth: 120,
          },
          {
            title: '类型',
            field: 'createdUserName',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return row.cameraType == null ? '--' : row.cameraType == 1 ? '枪机' : '球机'
              },
            },
          },
          {
            title: '所属渠系',
            field: 'chProjectName',
            minWidth: 100,
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 100,
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handlePlay(row)}>视频监控</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    mounted() {
      // 获取工程树
      getProjectCategoryTree({}).then(res => {
        this.projectOptions = res.data
      })
      this.getList()
    },
    methods: {
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.loading = true
        getCameraPage(this.queryParam).then(response => {
          this.cameraOptions = response?.data?.data?.map(el => ({ ...el, label: el.cameraName, value: el.cameraId }))
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      onChangeCamera(val) {
        this.queryCameraId = val
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        if (this.viewType === '1') {
          this.queryParam = {
            ...this.queryParam,
            cameraName: undefined,
            pageNum: 1,
          }
        } else if (this.viewType === '2') {
          this.queryParam = {
            ...this.queryParam,
            cameraType: undefined,
            treeNodeId: undefined,
            cameraName: undefined,
            pageNum: 1,
          }
        }

        this.handleQuery()
      },
      onTabChange() {
        this.queryParam.cameraName = undefined
        this.getList()
      },
      //点击视频
      handlePlay(row) {
        this.showCameraPlay = true
        this.$nextTick(() => this.$refs.cameraPlayRef.handleCamera(row))
      },
    },
  }
</script>
<style lang="less" scoped>
  .common-table-page {
    background: #fff;
    .top {
      padding: 10px 16px;
      display: flex;
      .query-box {
        display: flex;
        .name {
          margin-top: 6px;
        }
        .query-input {
          width: 200px;
        }
      }
      .query-radio-group {
        margin-left: auto;
        border: none;
        background: #f2f3f5;
        border-radius: 2px 2px 2px 2px;
      }
      .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
        color: #165dff;
        background: #fff;
        height: 26px;
        line-height: 23px;
        margin: 2px;
      }
      .ant-radio-button-wrapper {
        background: #f2f3f5;
        border-radius: 0;
      }
      .ant-radio-button-wrapper,
      .ant-radio-button-wrapper:first-child {
        border: none;
      }
      .ant-radio-button-wrapper:not(:first-child):before {
        background: transparent;
      }
    }
    .vxe-table-form {
      height: calc(100% - 40px);
    }
  }
</style>
