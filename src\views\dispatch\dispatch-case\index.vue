<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <!-- <a-form-item label="调度指令编号">
        <a-input
          v-model="queryParam.instructNumber"
          placeholder="请输入调度指令编号"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item> -->

      <a-form-item label="方案名称">
        <a-input
          v-model="queryParam.schemaName"
          placeholder="请输入方案名称"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <a-form-item label="调度时间段">
        <a-range-picker
          allow-clear
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          v-model="queryParam.dateRange"
          @openChange="() => (disabledDealDate = null)"
          @change="onRangeChange"
          @calendarChange="calendarChange"
        />
      </a-form-item>

      <a-form-item label="状态">
        <a-select
          allowClear
          v-model="queryParam.status"
          placeholder="请选择"
          :options="instructStateOptions"
          @change="handleQuery"
        ></a-select>
        <!-- <a-input
          v-model="queryParam.conferenceName"
          placeholder="请输入调度指令名称"
          allow-clear
          @keyup.enter.native="handleQuery"
        /> -->
      </a-form-item>

      <template #table>
        <!-- @selectChange="selectChange" -->
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" v-if="isCenter" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
            <!-- <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button> -->
          </div>
        </VxeTable>

        <FormGeneration
          v-if="showFormGeneration"
          ref="formGenerationRef"
          :radioOptions="radioOptions"
          :schemeTypeOptions="schemeTypeOptions"
          @ok="getList"
          @close="showFormGeneration = false"
          @showDetails="handleDetailsFromNew"
        />
        <IssuedWaterScheme
          v-if="showIssuedWater"
          ref="formIssuedWaterRef"
          @ok="getList"
          @close="showIssuedWater = false"
        />
        <DetailsWaterScheme
          v-if="showDetailsWater"
          ref="formDetailsWaterRef"
          :radioOptions="radioOptions"
          :schemeTypeOptions="schemeTypeOptions"
          :showEvaluateBtn="isFromNew"
          @showEvaluation="handleShowEvaluation"
          @ok="getList"
          @close="showDetailsWater = false"
        />
        <SchemeEvaluation
          v-if="showSchemeEvaluation"
          ref="formSchemeEvaluationRef"
          :currentSchemeDetails="currentSchemeDetails"
          @close="showSchemeEvaluation = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>
<script lang="jsx">
  import { getProjectTree, getOptions, getTreeByLoginOrgId, getUserDeptType } from '@/api/common'
  import { getSchemaPage, deleteSchema } from './services'
  // import { getSchemaPage } from '@/views/dispatch/schema/services'

  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import FormGeneration from './modules/index.vue'
  import IssuedWaterScheme from './modules/IssuedWaterScheme.vue'
  import DetailsWaterScheme from './modules/DetailsWaterScheme.vue'
  import SchemeEvaluation from './modules/SchemeEvaluation.vue'

  export default {
    name: 'DispatchInstruct',
    components: {
      VxeTable,
      VxeTableForm,
      FormGeneration,
      IssuedWaterScheme,
      DetailsWaterScheme,
      SchemeEvaluation,
    },
    data() {
      return {
        isCenter: false,
        exportLoading: false,
        radioOptions: [
          { label: '5日', value: 1 },
          { label: '旬', value: 2 },
          { label: '月', value: 3 },
        ],
        schemeTypeOptions: [
          { label: '用水计划', value: 1 },
          { label: '配水方案', value: 2 },
        ],
        showFormGeneration: false,
        showIssuedWater: false,
        showDetailsWater: false,
        showSchemeEvaluation: false,
        isFromNew: false, // 标记是否来自新增方案
        currentSchemeDetails: null, // 当前方案详情
        loginOrgId: JSON.parse(localStorage.getItem('user'))?.loginOrgId,
        deptOptions: [],
        schemaOptions: [],
        instructTypeOptions: [],
        instructStateOptions: [],
        showForm: false,
        list: [],
        tableTitle: '调度方案',
        loading: false,
        total: 0,
        queryParam: {
          dateRange: [],
          pageNum: 1,
          pageSize: 10,
          planType: '',
          schemaEndDate: '',
          schemaName: '',
          schemaStartDate: '',
          sort: [],
          status: undefined,

          sort: [],
          state: null,

          // conferenceCode: '',
          // conferenceName: '',
          // conferenceTheme: null,
          // conferenceTime: [],
          // endTime: '',
          // pageNum: 1,
          // pageSize: 10,
          // sort: [],
          // startTime: '',
        },
        columns: [
          { type: 'checkbox', width: 40 },
          {
            type: 'seq',
            title: '序号',
            width: 70,
            customRender: (_, r, idx) => idx + 1,
          },

          {
            title: '方案名称',
            field: 'schemaName',
            showOverflow: 'tooltip',
            minwidth: 240,
          },
          // {
          //   title: '主题',
          //   field: 'conferenceTheme',
          //   minwidth: '10%',
          //   slots: {
          //     default: ({ row, rowIndex }) => {
          //       return this.conferenceThemeOptions?.find(item => item.key == row.conferenceTheme)?.value
          //     },
          //   },
          // },
          // {
          //   title: '会商状态',
          //   field: 'conferenceStatus',
          //   minwidth: '10%',
          //   slots: {
          //     default: ({ row, rowIndex }) => {
          //       return this.conferenceStatusOptions?.find(item => item.key == row.conferenceStatus)?.value
          //     },
          //   },
          // },

          {
            title: '调度时间段',
            field: 'schemaStartDate',
            minwidth: 300,
            slots: {
              default: ({ row, rowIndex }) => {
                return `${row.schemaStartDate} 至 ${row.schemaEndDate}`
              },
            },
          },
          {
            title: '创建时间',
            field: 'createdTime',
            showOverflow: 'tooltip',
            minwidth: 180,
          },
          {
            title: '创建人',
            field: 'createdUserName',
            showOverflow: 'tooltip',
            minwidth: 120,
          },
          {
            title: '下发状态',
            field: 'status',
            showOverflow: 'tooltip',
            minwidth: 60,
            slots: {
              default: ({ row, rowIndex }) => {
                return row.status == 0 ? '待下发' : row.status == 1 ? '已下发' : '-'
                // return this.instructStateOptions?.find(item => item.value == row.status)?.label
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            slots: {
              default: ({ row, rowIndex }) => {
                if (this.loginOrgId == 10020) {
                  return (
                    <span>
                      <a onClick={() => this.handleCheck(row)}>查看</a>
                      <a-divider type='vertical' />
                      <a onClick={() => this.handleUpdate(row)}>编辑</a>

                      {this.isCenter && <a-divider type='vertical' />}
                      {this.isCenter && <a onClick={() => this.handleDelete(row)}>删除</a>}

                      {row.status == 0 && this.isCenter && <a-divider type='vertical' />}
                      {row.status == 0 && this.isCenter && <a onClick={() => this.handleIssued(row)}>下发</a>}
                    </span>
                  )
                  //1已下发  所只能看到已下发的
                } else {
                  return (
                    <span>
                      <a onClick={() => this.handleCheck(row)}>查看</a>
                      <a-divider type='vertical' />
                      <a onClick={() => this.handleUpdate(row)}>编辑</a>
                    </span>
                  )
                }
              },
            },
          },
        ],
      }
    },
    created() {
      this.init()
    },
    mounted() {},
    methods: {
      init() {
        getOptions('instructType').then(response => {
          this.instructTypeOptions = response?.data?.map(el => ({
            ...el,
            label: el.value,
            value: el.key,
          }))
        })
        getOptions('instructState').then(response => {
          this.instructStateOptions = response?.data?.map(el => ({
            ...el,
            label: el.value,
            value: el.key,
          }))
        })
        let schemaParam = {
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          planType: '',
          schemaEndDate: '',
          schemaName: '',
          schemaStartDate: '',
          sort: [],
          status: null,
        }
        // getSchemaPage(schemaParam).then(response => {
        //   console.log('***** 调度方案 res', response)
        //   this.schemaOptions = response?.data?.data?.map(el => ({
        //     ...el,
        //     label: el.schemaName,
        //     value: el.schemaId,
        //   }))
        //   console.log('***** 调度方案', this.schemaOptions)
        // })
        getTreeByLoginOrgId().then(response => {
          this.deptOptions = response?.data
          // this.deptOptions = response?.data?.map(el => ({
          //   ...el,
          //   label: el.deptName,
          //   value: el.deptId,
          // }))
        })
        let tmpDept = JSON.parse(localStorage.getItem('user')).deptId
        if (!tmpDept) return
        getUserDeptType({ deptId: tmpDept }).then(response => {
          this.isCenter = response.data == 1 ? true : false
        })
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.schemaStartDate = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') : null
        this.queryParam.schemaEndDate = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') : null
      },

      calendarChange(dates) {
        if (dates?.length == 1) {
          this.disabledDealDate = dates[0]
        }
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      getList() {
        this.showForm = false
        this.loading = true
        // this.selectChange({ records: [] })
        getSchemaPage(this.queryParam).then(response => {
          this.list = response?.data?.data || []
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      handleChange(value) {
        this.queryParam.unitId = value
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,

          planType: '',
          schemaEndDate: '',
          schemaName: '',
          schemaStartDate: '',
          sort: [],
          status: undefined,

          dateRange: [],
          // conferenceCode: '',
          // conferenceName: '',
          // conferenceTheme: null,
          // conferenceTime: [],
          // startTime: '',
          // endTime: '',
          // pageNum: 1,
          // sort: [],
          // type: 2,
        }

        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 导出
      handleExport() {
        this.exportLoading = false
      },
      //详情
      handleCheck(record) {
        this.isFromNew = false

        // 使用setTimeout确保DOM更新后再显示新组件
        setTimeout(() => {
          this.showDetailsWater = true

          // 再次使用setTimeout确保组件已经渲染完成
          setTimeout(() => {
            if (this.$refs.formDetailsWaterRef) {
              this.$refs.formDetailsWaterRef.handleDetails(record, 1)
            } else {
              console.error('未找到formDetailsWaterRef引用')
            }
          }, 100)
        }, 0)
      },
      /* 新增 */
      handleAdd() {
        this.showFormGeneration = true

        // 使用setTimeout确保组件已经渲染完成
        setTimeout(() => {
          if (this.$refs.formGenerationRef) {
            this.$refs.formGenerationRef.handleAdd()
          } else {
            console.error('未找到formGenerationRef引用')
          }
        }, 100)
      },
      // 处理方案评价
      handleShowEvaluation(scheme) {
        this.currentSchemeDetails = scheme
        this.showDetailsWater = false

        // 使用setTimeout确保DOM更新后再显示新组件
        setTimeout(() => {
          this.showSchemeEvaluation = true

          // 再次使用setTimeout确保组件已经渲染完成
          setTimeout(() => {
            if (this.$refs.formSchemeEvaluationRef) {
              this.$refs.formSchemeEvaluationRef.show(scheme)
            } else {
              console.error('未找到formSchemeEvaluationRef引用')
            }
          }, 100)
        }, 0)
      },
      // 处理新生成的方案详情
      handleDetailsFromNew(scheme) {
        this.isFromNew = true
        this.currentSchemeDetails = scheme

        // 先确保之前的组件都已关闭
        this.showFormGeneration = false

        // 使用setTimeout确保DOM更新后再显示新组件
        setTimeout(() => {
          this.showDetailsWater = true

          // 再次使用setTimeout确保组件已经渲染完成
          setTimeout(() => {
            if (this.$refs.formDetailsWaterRef) {
              this.$refs.formDetailsWaterRef.handleDetails(scheme)
            } else {
              console.error('未找到formDetailsWaterRef引用')
            }
          }, 100)
        }, 0)
      },
      /* 复制 */
      handleUpdate(record) {
        this.showDetailsWater = true

        // 使用setTimeout确保组件已经渲染完成
        setTimeout(() => {
          if (this.$refs.formDetailsWaterRef) {
            this.$refs.formDetailsWaterRef.handleDetails(record, 2)
          } else {
            console.error('未找到formGenerationRef引用')
          }
        }, 100)
      },
      /*下发 */
      handleIssued(record) {
        this.showIssuedWater = true

        // 使用setTimeout确保组件已经渲染完成
        setTimeout(() => {
          if (this.$refs.formIssuedWaterRef) {
            this.$refs.formIssuedWaterRef.handleIssued(record)
          } else {
            console.error('未找到formIssuedWaterRef引用')
          }
        }, 100)
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.schemaId ? [row?.schemaId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteSchema({ schemaIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                // that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
