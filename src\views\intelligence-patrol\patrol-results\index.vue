<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="巡渠路线">
        <a-select allowClear v-model="queryParam.lineId" :options="patrolRouteOptions" placeholder="请选择"></a-select>
      </a-form-item>

      <a-form-item label="状态">
        <a-select allowClear v-model="queryParam.taskStatus" :options="statusOptions" placeholder="请选择"></a-select>
      </a-form-item>

      <a-form-item label="任务时间">
        <a-range-picker
          allow-clear
          show-time
          style="width: 100%"
          v-model="rangeDate"
          :placeholder="['开始时间', '结束时间']"
        />
      </a-form-item>

      <a-form-item label="">
        <a-input v-model="queryParam.taskName" placeholder="请输入关键字" />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="巡检结果"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        ></VxeTable>

        <DetailsPatrolResults
          v-if="showDetailsPatrolResults"
          :patrolRouteOptions="patrolRouteOptions"
          ref="detailsPatrolResultsRef"
          @ok="getList"
          @close="showDetailsPatrolResults = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getPatrolResults, deletePatrolResults } from './services'
  import { getPatrolRoute } from '@/views/intelligence-patrol/patrol-route/services'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import DetailsPatrolResults from './modules/DetailsPatrolResults.vue'

  export default {
    name: 'PatrolPlan',
    components: {
      VxeTableForm,
      VxeTable,
      DetailsPatrolResults,
    },
    data() {
      return {
        showDetailsPatrolResults: false,
        patrolRouteOptions: [],

        showDetailsWaterLedger: false,
        statusOptions: [
          { label: '未巡检', value: 1 },
          { label: '巡检中', value: 2 },
          { label: '已巡检', value: 3 },
        ],

        rangeDate: [],
        list: [],
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          lineId: undefined,
          pageNum: 1,
          pageSize: 10,
          planEndTime: '',
          planStartTime: '',
          sort: [],
          taskName: '',
          taskStatus: undefined,
        },
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '任务编码',
            field: 'taskCode',
            minWidth: 100,
          },
          {
            title: '任务名称',
            field: 'taskName',
            minWidth: 130,
          },
          {
            title: '巡渠路线',
            field: 'lineName',
            minWidth: 100,
          },
          {
            title: '开始时间',
            field: 'planStartTime',
            minWidth: 105,
          },

          {
            title: '结束时间',
            field: 'planEndTime',
            minWidth: 105,
          },
          {
            title: '任务状态',
            minWidth: 75,
            slots: {
              default: ({ row, rowIndex }) => {
                return row.taskStatus == 1 ? '未巡检' : row.taskStatus == 2 ? '巡检中' : '已巡检'
              },
            },
          },
          {
            title: '视频点',
            minWidth: 35,
            slots: {
              default: ({ row }) => {
                return row?.lineObjectList?.length
              },
            },
          },
          {
            title: '异常点',
            minWidth: 55,
            slots: {
              default: ({ row }) => {
                return row.evenList?.length
              },
            },
          },

          {
            title: '操作',
            field: 'operate',
            width: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    beforeDestroy() {},
    created() {
      getPatrolRoute({}).then(res => {
        this.patrolRouteOptions = (res?.data || []).map(el => ({
          label: el.lineName,
          value: el.lineId,
        }))
      })
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showDetailsPatrolResults = false
        this.loading = true
        getPatrolResults({
          ...this.queryParam,
          planStartTime: this.rangeDate[0]?.format('YYYY-MM-DD HH:mm:ss'),
          planEndTime: this.rangeDate[1]?.format('YYYY-MM-DD HH:mm:ss'),
        }).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.rangeDate = []
        this.queryParam = {
          ...this.queryParam,
          lineId: undefined,
          pageNum: 1,
          planEndTime: '',
          planStartTime: '',
          taskName: '',
          taskStatus: undefined,
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      // selectChange(valObj) {
      //   this.ids = valObj.records.map(item => item.planReportId)
      //   this.names = valObj.records.map(item => item.serialNumber)
      //   this.isChecked = !!valObj.records.length
      // },
      // // 排序
      // sortChange(valObj) {
      //   this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
      //   this.getList()
      // },
      handleExport() {},
      /* 新增 */
      // handleAdd() {
      //   this.showDetailsPatrolResults = true
      //   this.$nextTick(() => this.$refs.formPatrolPlanRef.handleAdd())
      // },

      /* 修改 */
      // handleUpdate(record) {
      //   this.showDetailsPatrolResults = true
      //   this.$nextTick(() => this.$refs.formPatrolPlanRef.handleUpdate(record))
      // },
      //查看
      handleDetails(record) {
        this.showDetailsPatrolResults = true
        this.$nextTick(() => this.$refs.detailsPatrolResultsRef.handleDetails(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            return deletePatrolResults({ taskIds: row.taskId }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.getList()
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
