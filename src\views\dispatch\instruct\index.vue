<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="调度指令编号">
        <a-input
          v-model="queryParam.instructNumber"
          placeholder="请输入调度指令编号"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <a-form-item label="调度指令名称">
        <a-input
          v-model="queryParam.instructName"
          placeholder="请输入调度指令名称"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <a-form-item label="指令类型">
        <a-select
          allowClear
          v-model="queryParam.instructType"
          placeholder="请选择"
          :options="instructTypeOptions"
        ></a-select>
      </a-form-item>

      <a-form-item label="执行单位">
        <!-- <a-select allowClear v-model="queryParam.deptId" placeholder="请选择" :options="deptOptions"></a-select> -->

        <a-tree-select
          v-model="queryParam.deptId"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="deptOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'deptName',
            key: 'deptId',
            value: 'deptId',
          }"
          tree-default-expand-all
        ></a-tree-select>
        <!-- @keyup.enter.native="handleQuery" -->
      </a-form-item>
      <a-form-item label="调度时间段">
        <a-range-picker
          allow-clear
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          v-model="queryParam.dateRange"
          @openChange="() => (disabledDealDate = null)"
          @change="onRangeChange"
          @calendarChange="calendarChange"
        />
      </a-form-item>

      <a-form-item label="状态">
        <a-select allowClear v-model="queryParam.state" placeholder="请选择" :options="instructStateOptions"></a-select>
      </a-form-item>
      <a-form-item label="调度方案">
        <a-select allowClear v-model="queryParam.schemaId" placeholder="请选择" :options="schemaOptions"></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>

            <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
            <!-- <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button> -->
          </div>
        </VxeTable>
        <FormModal
          v-if="showForm"
          :chProjectOptions="chProjectOptions"
          ref="formRef"
          @ok="onOperationComplete"
          @close="showForm = false"
        />

        <DetailModal v-if="showDetailModal" ref="detailModalRef" @close="onOperationComplete" />
        <!-- <DetailModal v-if="showDetailModal" ref="detailModalRef" @close="showDetailModal = false" /> -->
        <SendSchemaInstruct
          v-if="showIssuedWater"
          ref="formIssuedWaterRef"
          @ok="getList"
          @close="showIssuedWater = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>
<script lang="jsx">
  import { getTreeWithCount, getOptions, getTreeByLoginOrgId } from '@/api/common'
  import { getSchemaInstructPage, deleteSchemaInstruct } from './services'
  import { getSchemaPage } from '@/views/dispatch/dispatch-case/services.js'
  import FormModal from './modules/FormModal.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import DetailModal from './modules/DetailModal.vue'
  import SendSchemaInstruct from './modules/SendSchemaInstruct.vue'

  export default {
    name: 'DispatchInstruct',
    components: {
      VxeTable,
      VxeTableForm,
      FormModal,
      DetailModal,
      SendSchemaInstruct,
    },
    data() {
      return {
        exportLoading: false,
        chProjectOptions: [],
        loginOrgId: JSON.parse(localStorage.getItem('user'))?.loginOrgId,
        showIssuedWater: false,
        deptOptions: [],
        schemaOptions: [],
        instructTypeOptions: [],
        instructStateOptions: [],
        showForm: false,
        showDetailModal: false,
        list: [],
        tableTitle: '调度指令',
        loading: false,
        total: 0,
        queryParam: {
          dateRange: [],
          deptId: undefined,
          instructEndDate: undefined,
          instructName: undefined,
          instructNumber: undefined,
          instructStartDate: undefined,
          instructType: undefined,
          pageNum: 1,
          pageSize: 10,
          schemaId: undefined,
          sort: [],
          state: undefined,
        },
        columns: [
          { type: 'checkbox', width: 40 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            customRender: (_, r, idx) => idx + 1,
          },
          {
            title: '调度令编号',
            field: 'instructNumber',
            showOverflow: 'tooltip',
            minWidth: 120,
          },
          {
            title: '调度令名称',
            field: 'instructName',
            showOverflow: 'tooltip',
            minWidth: 120,
          },
          {
            title: '指令类型',
            field: 'instructType',
            showOverflow: 'tooltip',
            minWidth: 80,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.instructTypeOptions?.find(item => item.value == row.instructType)?.label
              },
            },
          },
          {
            title: '调度时间段',
            field: 'instructStartDate', //instructEndDate
            minWidth: 220,
            // width: 280,
            slots: {
              default: ({ row, rowIndex }) => {
                return `${row.instructStartDate} 至 ${row.instructEndDate}`
              },
            },
          },
          {
            title: '调度方案',
            field: 'schemaName',
            showOverflow: 'tooltip',
            minWidth: 100,
          },
          {
            title: '执行单位',
            field: 'deptName',
            showOverflow: 'tooltip',
            minWidth: 100,
          },
          {
            title: '状态',
            field: 'state',
            minWidth: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.instructStateOptions?.find(item => item.value == row.state)?.label
              },
            },
          },
          {
            title: '下发状态',
            field: 'status',
            minWidth: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return row.status == 0 ? '待下发' : row.status == 1 ? '已下发' : '-'
                // return this.instructStateOptions?.find(item => item.value == row.status)?.label
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            minWidth: 160,
            slots: {
              default: ({ row, rowIndex }) => {
                if (this.loginOrgId == 10020) {
                  return (
                    <span>
                      <a onClick={() => this.handleDetail(row)}>查看</a>
                      {row.status == 0 && <a-divider type='vertical' />}
                      {row.status == 0 && <a onClick={() => this.handleEdit(row)}>修改</a>}

                      <a-divider type='vertical' />
                      <a onClick={() => this.handleDelete(row)}>删除</a>
                      {row.status == 0 && <a-divider type='vertical' />}
                      {row.status == 0 && <a onClick={() => this.handleIssued(row)}>下发</a>}
                    </span>
                  )
                } else {
                  return (
                    <span>
                      <a onClick={() => this.handleDetail(row)}>查看</a>
                    </span>
                  )
                }
              },
            },
          },
        ],
      }
    },
    created() {
      this.init()
    },
    mounted() {},
    methods: {
      init() {
        getOptions('instructType').then(response => {
          this.instructTypeOptions = response?.data?.map(el => ({
            ...el,
            label: el.value,
            value: el.key,
          }))
          console.log('***** 调度指令类型', this.instructTypeOptions, response)
        })
        getOptions('instructState').then(response => {
          this.instructStateOptions = response?.data?.map(el => ({
            ...el,
            label: el.value,
            value: el.key,
          }))
        })
        let schemaParam = {
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          planType: '',
          schemaEndDate: '',
          schemaName: '',
          schemaStartDate: '',
          sort: [],
          status: null,
        }
        getSchemaPage(schemaParam).then(response => {
          console.log('***** 调度方案 res', response)
          this.schemaOptions = response?.data?.data?.map(el => ({
            ...el,
            label: el.schemaName,
            value: el.schemaId,
          }))
          console.log('***** 调度方案', this.schemaOptions)
        })
        getTreeByLoginOrgId().then(response => {
          console.log('***** 部门树', response)
          this.deptOptions = response?.data
          // this.deptOptions = response?.data?.map(el => ({
          //   ...el,
          //   label: el.deptName,
          //   value: el.deptId,
          // }))
        })

        let param = { objectCategoryId: 3, districtCode: '', pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER, sort: [] }
        getTreeWithCount(param).then(res => {
          this.chProjectOptions = res?.data?.nodes?.map(el => ({
            ...el,
            label: el.projectName,
            value: el.projectId,
          }))
          console.log('getTreeWithCount res data', res, this.chProjectOptions)
        })
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.instructStartDate = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') : null
        this.queryParam.instructEndDate = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') : null
      },

      calendarChange(dates) {
        if (dates?.length == 1) {
          this.disabledDealDate = dates[0]
        }
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      getList() {
        this.showForm = false
        this.loading = true
        // this.selectChange({ records: [] })
        getSchemaInstructPage(this.queryParam).then(response => {
          this.list = response?.data?.data || []
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      handleChange(value) {
        this.queryParam.unitId = value
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /*下发 */
      handleIssued(record) {
        this.showIssuedWater = true

        // 使用setTimeout确保组件已经渲染完成
        setTimeout(() => {
          if (this.$refs.formIssuedWaterRef) {
            this.$refs.formIssuedWaterRef.handleIssued(record)
          } else {
            console.error('未找到formIssuedWaterRef引用')
          }
        }, 100)
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,

          deptId: null,
          instructEndDate: '',
          instructName: '',
          instructNumber: '',
          instructStartDate: '',
          instructType: null,
          schemaId: null,
          sort: [],
          state: null,
          dateRange: [],
        }

        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 详情
      handleDetail(row) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleShow(row))
      },
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handleAdd())
      },
      // 导出
      handleExport() {},
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handleUpdate(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        console.log('del 408', row)
        const ids = row?.instructId
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteSchemaInstruct({ instructIds: ids }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                // that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
