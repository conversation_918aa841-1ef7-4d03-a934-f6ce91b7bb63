<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="巡渠路线">
        <a-select allowClear v-model="queryParam.lineId" :options="patrolRouteOptions" placeholder="请选择"></a-select>
      </a-form-item>

      <a-form-item label="状态">
        <a-select allowClear v-model="queryParam.isStopped" :options="statusOptions" placeholder="请选择"></a-select>
      </a-form-item>

      <a-form-item label="计划时间">
        <a-range-picker allow-clear style="width: 100%" v-model="rangeDate" :placeholder="['开始时间', '结束时间']" />
      </a-form-item>

      <a-form-item label="">
        <a-input v-model="queryParam.planName" placeholder="请输入关键字" />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="巡渠计划"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="primary" @click="handleExport()" icon="download" :loading="exportLoading">导出</a-button>
          </div>
        </VxeTable>

        <FormPatrolPlan
          v-if="showFormPatrolPlan"
          :patrolRouteOptions="patrolRouteOptions"
          :isInfiniteOptions="isInfiniteOptions"
          :patrolUnitOptions="patrolUnitOptions"
          :cameraUnitOptions="cameraUnitOptions"
          ref="formPatrolPlanRef"
          @ok="getList"
          @close="showFormPatrolPlan = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getPatrolPlanList, deletePatrolPlan, startPatrolPlan, stopPatrolPlan } from './services'
  import { getPatrolRoute } from '@/views/intelligence-patrol/patrol-route/services'
  import FormPatrolPlan from './modules/FormPatrolPlan'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'

  export default {
    name: 'PatrolPlan',
    components: {
      VxeTableForm,
      VxeTable,
      FormPatrolPlan,
    },
    data() {
      return {
        showFormPatrolPlan: false,
        patrolRouteOptions: [],

        radioOptions: [
          { label: '5日', value: 1 },
          { label: '旬', value: 2 },
          { label: '月', value: 3 },
        ],

        showDetailsWaterLedger: false,
        statusOptions: [
          { label: '启用', value: 0 },
          { label: '禁用', value: 1 },
        ],

        isInfiniteOptions: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
        patrolUnitOptions: [
          { label: '天', value: 1 },
          { label: '周', value: 2 },
          { label: '月', value: 3 },
        ],
        cameraUnitOptions: [
          { label: '秒', value: 1 },
          { label: '分', value: 2 },
          { label: '时', value: 3 },
        ],

        rangeDate: [],
        list: [],
        exportLoading: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          isStopped: undefined,
          lineId: undefined,
          pageNum: 1,
          pageSize: 10,
          planEndDate: '',
          planName: '',
          planStartDate: '',
        },
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '计划编号',
            field: 'planCode',
            minWidth: 130,
          },
          {
            title: '计划名称',
            field: 'planName',
            minWidth: 90,
          },
          {
            title: '巡渠路线',
            field: 'lineName',
            minWidth: 100,
          },
          {
            title: '计划开始日期',
            field: 'planStartDate',
            minWidth: 105,
          },
          {
            title: '计划结束日期',
            field: 'planEndDate',
            minWidth: 105,
          },
          {
            title: '视频点',
            field: 'planEndDate',
            minWidth: 60,
            slots: {
              default: ({ row }) => {
                return row?.lineObjectList?.length
              },
            },
          },
          {
            title: '视频点停留时间',
            minWidth: 105,
            slots: {
              default: ({ row }) => {
                return row.cameraPeriod + this.cameraUnitOptions.find(el => el.value == row.cameraPeriodUnit)?.label
              },
            },
          },
          {
            title: '巡检周期',
            minWidth: 80,
            slots: {
              default: ({ row }) => {
                return row.patrolPeriod + this.patrolUnitOptions.find(el => el.value == row.patrolPeriodUnit)?.label
              },
            },
          },
          {
            title: '状态',
            field: 'isStopped',
            minWidth: 60,
            slots: {
              default: ({ row, rowIndex }) => {
                return row.isStopped == 0 ? '启用' : '禁用'
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    {row.reuseTasks?.length == 0 && row.isStopped == 1 && (
                      <span>
                        <a onClick={() => this.handleEdit(row)}>修改</a>
                        <a-divider type='vertical' />
                      </span>
                    )}
                    <a onClick={() => this.handleStatus(row)}>{row.isStopped == 0 ? '禁用' : '启用'}</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    beforeDestroy() {},
    created() {
      getPatrolRoute({}).then(res => {
        this.patrolRouteOptions = (res?.data || []).map(el => ({
          label: el.lineName,
          value: el.lineId,
        }))
      })
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showFormPatrolPlan = false
        this.loading = true
        getPatrolPlanList({
          ...this.queryParam,
          planStartDate: this.rangeDate[0]?.format('YYYY-MM-DD'),
          planEndDate: this.rangeDate[1]?.format('YYYY-MM-DD'),
        }).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.rangeDate = []
        this.queryParam = {
          ...this.queryParam,
          isStopped: undefined,
          lineId: undefined,
          pageNum: 1,
          planEndDate: '',
          planName: '',
          planStartDate: '',
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showFormPatrolPlan = true
        this.$nextTick(() => this.$refs.formPatrolPlanRef.handleAdd())
      },
      handleStatus(row) {
        var that = this
        let urlApi = row.isStopped == 0 ? stopPatrolPlan : startPatrolPlan
        this.$confirm({
          title: '确认' + row.isStopped == 0 ? '禁用' : '启用' + '所选中数据?',
          content: '请确认是否' + row.isStopped == 0 ? '禁用' : '启用' + '当前选中的数据',

          onOk() {
            return urlApi({ planId: row.planId }).then(res => {
              that.$message.success(`成功`, 3)
              that.getList()
            })
          },
          onCancel() {},
        })
      },
      /* 修改 */
      handleEdit(record) {
        this.showFormPatrolPlan = true
        this.$nextTick(() => this.$refs.formPatrolPlanRef.handleUpdate(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            return deletePatrolPlan({ planIds: row.planId }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.getList()
            })
          },
          onCancel() {},
        })
      },
      //导出
      handleExport() {
        this.exportLoading = true
        getPatrolPlanList({
          ...this.queryParam,
          planStartDate: this.rangeDate[0]?.format('YYYY-MM-DD'),
          planEndDate: this.rangeDate[1]?.format('YYYY-MM-DD'),
        }).then(res => {
          this.exportLoading = false

          const columnsList = [
            {
              title: '序号',
              field: 'seq',
              minWidth: 80,
            },
            {
              title: '计划编号',
              field: 'planCode',
              minWidth: 130,
            },
            {
              title: '计划名称',
              field: 'planName',
              minWidth: 90,
            },
            {
              title: '巡渠路线',
              field: 'lineName',
              minWidth: 100,
            },
            {
              title: '计划开始日期',
              field: 'planStartDate',
              minWidth: 105,
            },
            {
              title: '计划结束日期',
              field: 'planEndDate',
              minWidth: 105,
            },
            {
              title: '视频点',
              field: 'planEndDate',
              minWidth: 60,
              slots: {
                default: ({ row }) => {
                  return row?.lineObjectList?.length
                },
              },
            },
            {
              title: '视频点停留时间',
              minWidth: 105,
              field: 'cameraPeriod',
            },
            {
              title: '巡检周期',
              minWidth: 80,
              field: 'patrolPeriod',
            },
            {
              title: '状态',
              field: 'isStopped',
              minWidth: 60,
            },
          ]

          const data = (res.data?.data || []).map((row, i) => ({
            ...row,
            seq: i + 1,

            cameraPeriod: row.cameraPeriod + this.cameraUnitOptions.find(el => el.value == row.cameraPeriodUnit)?.label,
            patrolPeriod: row.patrolPeriod + this.patrolUnitOptions.find(el => el.value == row.patrolPeriodUnit)?.label,

            isStopped: row.isStopped == 0 ? '启用' : '禁用',
          }))

          excelExport(columnsList, data, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
