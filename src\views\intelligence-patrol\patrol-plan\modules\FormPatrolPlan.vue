<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1000"
    @cancel="cancel"
    modalHeight="600"
  >
    <div slot="content">
      <div layout="horizontal">
        <a-form-model
          ref="form"
          :model="form"
          :rules="rules"
          layout="horizontal"
          v-bind="{
            labelCol: { span: 8 },
            wrapperCol: { span: 16 },
          }"
        >
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="计划名称" prop="planName">
                <a-input v-model="form.planName" allowClear placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="巡渠路线" prop="lineId">
                <a-select
                  allowClear
                  v-model="form.lineId"
                  :options="patrolRouteOptions"
                  placeholder="请选择"
                ></a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="到期是否自动循环" prop="isInfinite">
                <a-select
                  allowClear
                  v-model="form.isInfinite"
                  :options="isInfiniteOptions"
                  placeholder="请选择"
                ></a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="计划开始日期" prop="planStartDate">
                <a-date-picker
                  v-model="form.planStartDate"
                  valueFormat="YYYY-MM-DD"
                  :disabledDate="disabledDate"
                  placeholder="请选择"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="计划结束日期" prop="planEndDate">
                <a-date-picker
                  :disabled="form.isInfinite == 1"
                  v-model="form.planEndDate"
                  valueFormat="YYYY-MM-DD"
                  :disabledDate="disabledDate"
                  placeholder="请选择"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="巡检周期" prop="patrolPeriod">
                <a-input
                  style="width: calc(100% - 100px)"
                  v-model="form.patrolPeriod"
                  placeholder="请输入"
                  allowClear
                />
                <a-select
                  style="width: 100px"
                  allowClear
                  v-model="form.patrolPeriodUnit"
                  :options="patrolUnitOptions"
                  placeholder="请选择"
                ></a-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="视频点停留时间" prop="cameraPeriod">
                <a-input
                  style="width: calc(100% - 100px)"
                  v-model="form.cameraPeriod"
                  placeholder="请输入"
                  allowClear
                />
                <a-select
                  style="width: 100px"
                  allowClear
                  v-model="form.cameraPeriodUnit"
                  :options="cameraUnitOptions"
                  placeholder="请选择"
                ></a-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item
                label="备注"
                v-bind="{
                  labelCol: { span: 4 },
                  wrapperCol: { span: 19 },
                }"
              >
                <a-textarea v-model="form.remark" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addPatrolPlan, getPatrolPlanDetails, editPatrolPlan } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormUseWater',
    components: { AntModal },
    props: ['patrolRouteOptions', 'isInfiniteOptions', 'patrolUnitOptions', 'cameraUnitOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        formTitle: '',

        form: {
          cameraPeriod: undefined,
          cameraPeriodUnit: 1,
          isInfinite: undefined,
          lineId: undefined,
          patrolPeriod: undefined,
          patrolPeriodUnit: 2,
          planEndDate: '',
          planName: '',
          planStartDate: '',
          remark: '',
        },
        rules: {
          planName: [{ required: true, message: '计划名称不能为空', trigger: 'blur' }],
          lineId: [{ required: true, message: '巡渠路线不能为空', trigger: 'change' }],
          isInfinite: [{ required: true, message: '到期是否自动循环不能为空', trigger: 'change' }],
          planStartDate: [{ required: true, message: '计划开始日期不能为空', trigger: 'change' }],
          patrolPeriod: [{ required: true, message: '巡检周期不能为空', trigger: 'blur' }],
          cameraPeriod: [{ required: true, message: '视频点停留时间不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      disabledDate(current) {
        return current && current < moment().endOf('day')
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.open = true
        this.formTitle = '新增'
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '编辑'
        this.modalLoading = true
        getPatrolPlanDetails({ planId: record.planId }).then(res => {
          this.form = res?.data
          this.modalLoading = false
        })
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.form.planId !== undefined) {
              editPatrolPlan(this.form)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                })
                .finally(() => (this.loading = false))
            } else {
              addPatrolPlan(this.form)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                })
                .finally(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped></style>
