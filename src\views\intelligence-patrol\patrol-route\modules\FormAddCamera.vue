<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="560"
  >
    <div slot="content">
      <div class="table-panel">
        <a-form-model ref="form" :model="form" :rules="rules" layout="horizontal">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="视频点" prop="checkedCamera">
                <a-select
                  allowClear
                  mode="multiple"
                  show-search
                  :filter-option="filterOption"
                  v-model="form.checkedCamera"
                  :options="cameraOptions"
                  placeholder="请选择"
                ></a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addPatrolCamera } from '../services.js'
  import { getCameraPage } from '@/views/basic/camera/services.js'
  import AntModal from '@/components/pt/dialog/AntModal'
  import { Row } from 'vxe-pc-ui'

  export default {
    name: 'FormPatrolRoute',
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '',

        cameraOptions: [],

        lineId: undefined,
        form: {
          checkedCamera: [],
        },
        open: false,
        rules: {
          checkedCamera: [{ required: true, message: '视频点不能为空', trigger: 'change' }],
          sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
        },
      }
    },
    filters: {},
    created() {
      getCameraPage({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
        treeNodeType: 'category',
      }).then(response => {
        this.cameraOptions = response?.data?.data?.map(el => ({ label: el.cameraName, value: el.cameraId }))
      })
    },
    computed: {},
    watch: {},
    methods: {
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 添加视频按钮操作 */
      handleAddCamera(record) {
        this.open = true
        this.modalTitle = '添加视频点'
        this.lineId = record.lineId
      },
      handleEditCamera(record) {
        this.open = true
        this.modalTitle = '修改视频点'
        this.lineId = record.lineId
        this.form.checkedCamera = []
        record.lineObjectList.forEach(el => {
          this.form.checkedCamera.push(el.cameraId)
        })
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const params = []
            this.form.checkedCamera.forEach((el, i) => {
              params.push({
                cameraId: el,
                lineId: this.lineId,
                sort: i,
              })
            })
            addPatrolCamera(params)
              .then(response => {
                this.$message.success('成功', 3)
                this.loading = false
                this.open = false
                this.$emit('ok')
              })
              .catch(() => (this.loading = false))
          } else {
            return false
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped></style>
