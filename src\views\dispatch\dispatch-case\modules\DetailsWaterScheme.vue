<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1300"
    @cancel="cancel"
    modalHeight="830"
  >
    <div slot="content">
      <div>
        <h3>基本信息</h3>
        <a-row class="basic-info">
          <a-col :lg="8" :md="8" :sm="8" :span="8">
            方案名称：
            {{ schemeDetails?.schemaName }}
          </a-col>
          <a-col :lg="8" :md="8" :sm="8" :span="8">
            调度时间段：
            {{ schemeDetails?.schemaStartDate }}~{{ schemeDetails?.schemaEndDate }}
          </a-col>
        </a-row>
        <!-- <a-row>
          <a-col :lg="8" :md="8" :sm="8" :span="8">
            创建时间：{{ schemeDetails?.createdTime }}
          </a-col>
          <a-col :lg="8" :md="8" :sm="8" :span="8">创建人：{{ schemeDetails?.createdUserName }}</a-col>
        </a-row> -->
        <div class="dispatch-content">
          <h3>方案信息</h3>
          <div class="scheme-container">
            <div class="scheme-tabs">
              <div
                v-for="(tab, index) in schemeTabs"
                :key="index"
                :class="['scheme-tab', { active: selectedSchemeIndex === index }]"
                @click="selectSchemeTab(index)"
              >
                {{ tab.name }}
              </div>
            </div>
            <div class="action-buttons">
              <div class="action-btn" :class="{ active: selectedButton === 'data' }" @click="selectButton('data')">
                数据表
              </div>
              <div
                class="action-btn"
                :class="{ active: selectedButton === 'overview' }"
                @click="selectButton('overview')"
              >
                概化图
              </div>
              <div class="action-btn" :class="{ active: selectedButton === 'twoD' }" @click="selectButton('twoD')">
                二维推演
              </div>
            </div>
          </div>
          <!-- 数据表 -->
          <div class="canal-tabs-wrapper" v-if="selectedButton === 'data' && canalTabs.length > 0">
            <div class="canal-tabs-container">
              <div class="canal-tabs">
                <div
                  v-for="(canal, index) in canalTabs"
                  :key="index"
                  :class="['canal-tab', { active: selectedCanalIndex === index }]"
                  @click="selectCanalTab(index, canal)"
                >
                  {{ canal.depName }}
                </div>
              </div>
            </div>
          </div>

          <div class="canal-tabs-wrapper" v-if="selectedButton === 'data' && canalTabs2.length > 0">
            <div class="canal-tabs-container">
              <div class="canal-tabs">
                <div
                  v-for="(canal, index) in canalTabs2"
                  :key="index"
                  :class="['canal-tab', { active: selectedCanalIndex2 === index }]"
                  @click="selectCanalTab2(index, canal)"
                >
                  {{ canal.chName }}
                </div>
              </div>
            </div>
            <div class="flow-info">注:1Q日=8.64万m³</div>
          </div>

          <div class="content-container">
            <div v-if="selectedButton === 'data'" class="data-content">
              <!-- 手风琴组件 -->
              <div class="accordion" v-if="accordionItems?.length > 0">
                <div
                  class="accordion-item"
                  v-for="(item, index) in accordionItems"
                  :key="index"
                  :class="{ expanded: item.expanded }"
                >
                  <div class="accordion-header" @click="toggleAccordion(index)">
                    <div class="accordion-title">
                      <a-icon :type="item.expanded ? 'down' : 'right'" />
                      <span>{{ item.title }}</span>
                    </div>
                    <div class="accordion-actions" v-if="type == 2">
                      <a-button v-if="!item.editing" type="link" size="small" @click.stop="startEditing(index)">
                        编辑
                      </a-button>
                      <template v-if="item.editing">
                        <a-button type="link" size="small" @click.stop="cancelEditing(index)">取消</a-button>
                        <a-button type="primary" size="small" @click.stop="saveEditing(index)">保存</a-button>
                      </template>
                    </div>
                  </div>
                  <div class="accordion-content" v-show="item.expanded">
                    <a-table
                      :columns="schemeColumns"
                      :data-source="item.data"
                      :pagination="false"
                      size="middle"
                      bordered
                    />
                  </div>
                </div>

                <!-- 折叠的项目 -->
                <div
                  class="accordion-item collapsed"
                  v-for="(item, index) in collapsedItems"
                  :key="'collapsed-' + index"
                >
                  <div class="accordion-header" @click="expandCollapsedItem(index)">
                    <div class="accordion-title">
                      <a-icon type="right" />
                      <span>{{ item.title }}</span>
                    </div>
                    <div class="accordion-actions">
                      <a-button type="link" size="small" @click.stop="startEditing(index + accordionItems.length)">
                        编辑
                      </a-button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion" v-else>
                <a-empty style="margin-top: 20px" />
              </div>
            </div>

            <!-- 概化图 -->
            <div v-if="selectedButton === 'overview'" class="tab-content" style="display: flex;flex-direction: row;">
              <GeneralizedMap style="flex: 5;" :mapData="modelChannelResGateFlowVOList" ></GeneralizedMap>
              <GeneralizedTable style="flex: 5;" :dataSource="modelChannelResGateWlVOList"></GeneralizedTable>
            </div>
            <!-- 二维推演 -->
            <div v-if="selectedButton === 'twoD'" class="tab-content">
              <Simulation :dataSource="modelChannelResStakeWlVOList"></Simulation>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template slot="footer">
      <template v-if="showEvaluateBtn">
        <a-button type="primary" @click="showEvaluationModal">方案评价</a-button>
      </template>
      <template v-else>
        <a-button type="primary" v-if="type == 2" @click="submitForm" :loading="loading">提交</a-button>
        <a-button @click="cancel">取消</a-button>
      </template>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { issuedSchema, getSchema, getLastChList, getSchemaEvaluates } from '../services.js'
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable/index.vue'
  import GeneralizedTable from './GeneralizedTable.vue'
  import GeneralizedMap from './GeneralizedMap.vue'
  import Simulation from './Simulation.vue'
  import { getReportFlows } from '@/views/custom/water-resource/supply-water-plan/services.js'
  import moment from 'moment'
  import { dealNumber, getFixedNum, toChineseNum } from '@/utils/dealNumber.js'
  import * as _ from 'lodash'

  export default {
    name: 'DetailsWaterScheme',
    components: { AntModal, VxeTable, GeneralizedMap, GeneralizedTable, Simulation },
    props: ['radioOptions', 'showEvaluateBtn', 'schemeTypeOptions'],
    data() {
      return {
        type: null,
        activeKey: '1',
        loading: false,
        modalLoading: false,
        open: false,
        schemeDetails: {},

        formTitle: '调度方案详情',
        form: {},
        open: false,
        rules: {},

        columns: [],
        tableData: [],
        summaries: [],

        // 方案Tab数据
        schemeTabs: [{ name: '方案一', id: 1 }],
        selectedSchemeIndex: 0,

        // 干渠Tab数据
        canalTabs: [],
        canalTabs2: [],
        selectedCanalIndex: 0,
        selectedCanalIndex2: 0,

        // 手风琴数据
        accordionItems: [],

        // 折叠状态的项目
        collapsedItems: [],

        // 表格列定义
        schemeColumns: [
          // { title: '序号', dataIndex: 'key', key: 'key', align: 'center' },

          { title: '日期', dataIndex: 'localDate', key: 'localDate', align: 'center' },
          {
            title: '开闸时间',
            dataIndex: 'openDate',
            key: 'openDate',
            align: 'center',
            ellipsis: true,
            customRender: (v, record, i) => {
              // 获取父级手风琴项的编辑状态
              const editing =
                this.accordionItems.find(item => item.data && item.data.some(d => d.key === record.key))?.editing ||
                false

              return (
                <div class='table-cell-box'>
                  {/* {editing ? (
                    <a-time-picker size='small' v-model={this.formatTime(record.openDate)} format='HH:mm' />
                  ) : (
                    <span>{record.openDate}</span>
                  )} */}
                  {/* <a-time-picker size='small' v-model={moment(record.openDate).format('HH:mm')} format='HH:mm' /> */}
                  {/* <a-time-picker :default-value="moment({record.openDate}, 'HH:mm')" format="HH:mm" /> */}
                  {editing ? <a-input size='small' v-model={record.openDate} /> : <span>{record.openDate}</span>}
                </div>
              )
            },
          },
          {
            title: '关闭时间',
            dataIndex: 'closeDate',
            key: 'closeDate',
            align: 'center',
            ellipsis: true,
            customRender: (v, record, i) => {
              // 获取父级手风琴项的编辑状态
              const editing =
                this.accordionItems.find(item => item.data && item.data.some(d => d.key === record.key))?.editing ||
                false

              return (
                <div class='table-cell-box'>
                  {editing ? <a-input size='small' v-model={record.closeDate} /> : <span>{record.closeDate}</span>}
                </div>
              )
            },
          },
          {
            title: '调度流量(Q日)',
            dataIndex: 'flow',
            key: 'flow',
            align: 'center',
            ellipsis: true,
            customRender: (v, record, i) => {
              // 获取父级手风琴项的编辑状态
              const editing =
                this.accordionItems.find(item => item.data && item.data.some(d => d.key === record.key))?.editing ||
                false

              return (
                <div class='table-cell-box'>
                  {editing ? (
                    <a-input-number size='small' v-model={record.flow} min={0} precision={1} style='width: 100%' />
                  ) : (
                    <span>{record.flow}</span>
                  )}
                </div>
              )
            },
          },
          {
            title: '开度(%)',
            dataIndex: 'openValue',
            key: 'openValue',
            align: 'center',
            ellipsis: true,
            customRender: (v, record, i) => {
              // 获取父级手风琴项的编辑状态
              const editing =
                this.accordionItems.find(item => item.data && item.data.some(d => d.key === record.key))?.editing ||
                false

              return (
                <div class='table-cell-box'>
                  {editing ? (
                    <a-input-number size='small' v-model={record.openValue} min={0} precision={1} style='width: 100%' />
                  ) : (
                    <span>{record.openValue}</span>
                  )}
                </div>
              )
            },
          },
          {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
            ellipsis: true,
            customRender: (v, record, i) => {
              // 获取父级手风琴项的编辑状态
              const editing =
                this.accordionItems.find(item => item.data && item.data.some(d => d.key === record.key))?.editing ||
                false

              return (
                <div class='table-cell-box'>
                  {editing ? <a-input size='small' v-model={record.remark} /> : <span>{record.remark}</span>}
                </div>
              )
            },
            // scopedSlots: { customRender: 'remarkInput' },
          },
        ],
        selectedButton: 'data',

        // 方案评价相关
        showEvaluate: false,
        evaluationData: {
          plan1: {
            completionRate: 80,
            stability: 90,
            timeliness: 90,
          },
          plan2: {
            completionRate: 80,
            stability: 90,
            timeliness: 90,
          },
        },
        // 概化图数据
        modelChannelResGateFlowVOList: [],
        // 概化图所属表格数据
        modelChannelResGateWlVOList: [],
        // 二维推演数据
        modelChannelResStakeWlVOList: [],
      }
    },
    filters: {},
    created() {
      this.init()
    },
    computed: {},
    mounted() {
      console.log('**** ************ 111 mounted', this.radioOptions, this.showEvaluateBtn, this.schemeTypeOptions)
    },
    watch: {},
    methods: {
      init() {
        // getLastChList().then(res => {
        //   this.canalTabs = res.data
        //   this.selectCanalTab(0, this.canalTabs[0])
        //   console.log('**** ************ 111 res', res, this.canalTabs)
        // })
      },
      handleClose() {
        this.open = false
      },
      selectSchemeTab(index) {
        this.selectedSchemeIndex = index
        console.log('Selected scheme tab:', this.schemeTabs[index].id)
        this.getDetails(this.schemeDetails, this.schemeTabs[index]?.id)
      },
      selectCanalTab(index, canal) {
        this.selectedCanalIndex = index
        this.canalTabs2 = this.canalTabs[index]?.projectWlvs || []
        console.log('Selected canal tab 2222:', this.canalTabs2)
        this.selectedButton = 'data'
        this.selectCanalTab2(0, this.canalTabs2[0])
        // let arr = this.canalTabs[index].projectWlvs
        // console.log('Selected canal tab:21', this.canalTabs2, index)
        // console.log('Selected canal tab:22', this.accordionItems)
        // console.log('Selected canal tab:23', this.canalTabs[index].name)
      },
      selectCanalTab2(index, canal2) {
        this.selectedCanalIndex2 = index

        this.accordionItems = this.canalTabs2[index]?.projects?.map(item => ({
          ...item,
          key: item.projectId, // key 设置为 chId
          expanded: true, // 默认展开
          editing: false, // 默认非编辑状态
          title: item.projectName, // title 设置为 projectName
          data: item.gateResVOS, // data 设置为 projects 数组
        }))
        console.log('********** 515 获取手风琴数据 333333', this.accordionItems)
      },
      toggleAccordion(index) {
        this.accordionItems[index].expanded = !this.accordionItems[index].expanded
      },
      startEditing(index) {
        if (index < this.accordionItems.length) {
          this.accordionItems[index].editing = true
          // 确保项目是展开的
          this.accordionItems[index].expanded = true
        }
      },
      cancelEditing(index) {
        this.accordionItems[index].editing = false
      },
      saveEditing(index) {
        this.accordionItems[index].editing = false
        // 在这里可以添加保存数据的逻辑
      },
      expandCollapsedItem(index) {
        // 将折叠项移动到展开的手风琴项目中
        const item = this.collapsedItems[index]
        this.accordionItems.push({
          ...item,
          expanded: true,
          editing: false,
        })
        // 从折叠项中移除
        this.collapsedItems.splice(index, 1)
      },
      selectButton(button) {
        this.selectedButton = button
        console.log('Selected button:', button)
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      formatTime(time) {
        return moment(time).format('HH:mm')
      },
      sumNum1(list, date, field) {
        let count = 0
        list.forEach((item, idx) => {
          if (idx > 2) {
            count += Number(item.recordObj[date][field])
          }
        })
        return getFixedNum(count, 1)
      },
      sumNum2(list, date, field) {
        let count = 0
        list.forEach((item, idx) => {
          count += Number(item.recordObj[date][field])
        })
        return getFixedNum(count, 1)
      },
      footerMethod({ columns, data }) {
        const footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 2) {
              return '永济渠合计'
            }
            if (_columnIndex > 2 && _columnIndex < columns.length - 1) {
              return `${this.sumNum1(data, column.property, 'planStartFlow')} ~ ${this.sumNum1(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '永济渠报总局'
            }
            if (_columnIndex > 2 && _columnIndex < columns.length - 1) {
              return `${this.sumNum2(data, column.property, 'planStartFlow')} ~ ${this.sumNum2(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '备注'
            }
            if (_columnIndex > 2 && _columnIndex < columns.length - 1) {
              return `${this.sumNum2(data, column.property, 'planStartFlow')} ~ ${this.sumNum2(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
        ]
        return footerData
      },
      dealColumns() {
        const dateRange = [moment(this.schemeDetails.planStartDate), moment(this.schemeDetails.planEndDate)]

        this.columns = [
          {
            title: '所名',
            field: 'deptName',
            align: 'center',
            width: 40,
            fixed: 'left',
            headerClassName: 'custmer-span',
            slots: {
              default: () => (
                <div style='writing-mode: vertical-rl;width: 100%; display: flex; align-items: center; letter-spacing: 6px'>
                  永济灌区
                </div>
              ),
            },
          },
          {
            title: '所名',
            field: 'deptName',
            align: 'center',
            minWidth: 120,
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
          },
          {
            title: '所名',
            field: 'deptName',
            align: 'center',
            minWidth: 120,
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
          },
          // 生成日期列
          ...[...Array(dateRange[1].diff(dateRange[0], 'day') + 1)].map((item, index) => {
            const date = moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD')

            return {
              title: moment(dateRange[0]).add(index, 'day').format('MM月DD日'),
              align: 'center',
              minWidth: 120,
              field: moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD'),
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number
                        size='small'
                        v-model={this.tableData[rowIndex].recordObj[date].planStartFlow}
                        min={0}
                        precision={1}
                      />
                      &nbsp;~&nbsp;
                      <a-input-number
                        size='small'
                        v-model={this.tableData[rowIndex].recordObj[date].planEndFlow}
                        min={0}
                        precision={1}
                      />
                    </div>
                  )
                },
                footer: rowInfo => {
                  // 永济渠合计
                  if (rowInfo.rowIndex === 0) {
                    return rowInfo.row[rowInfo.itemIndex]
                  }
                  // 永济渠报总局
                  if (rowInfo.rowIndex === 1) {
                    if (rowInfo.itemIndex < 2 || rowInfo.itemIndex === rowInfo.items.length - 1)
                      return rowInfo.row[rowInfo.itemIndex]

                    return (
                      <div class='table-cell-box'>
                        <a-input-number
                          size='small'
                          v-model={this.summaries[rowInfo.itemIndex - 3].planStartFlow}
                          min={0}
                          precision={1}
                          onChange={val => {
                            this.$nextTick(() => {
                              const obj = this.summaries[rowInfo.itemIndex - 3]
                              if (obj.planStartFlow > obj.planEndFlow) {
                                this.summaries[rowInfo.itemIndex - 3].planStartFlow = obj.planEndFlow
                              }
                            })
                          }}
                        />
                        &nbsp;~&nbsp;
                        <a-input-number
                          size='small'
                          v-model={this.summaries[rowInfo.itemIndex - 3].planEndFlow}
                          min={0}
                          precision={1}
                          onChange={val => {
                            this.$nextTick(() => {
                              const obj = this.summaries[rowInfo.itemIndex - 3]
                              if (obj.planStartFlow > obj.planEndFlow) {
                                this.summaries[rowInfo.itemIndex - 3].planEndFlow = obj.planStartFlow
                              }
                            })
                          }}
                        />
                      </div>
                    )
                  }
                  if (rowInfo.rowIndex === 2) {
                    return <a-input size='small' v-model={this.remarks} />
                  }
                },
              },
            }
          }),

          {
            title: '备注',
            field: 'remark',
            align: 'center',
            minWidth: 120,
            showOverflow: true,
            slots: {
              default: ({ row, rowIndex }) => {
                return <a-input size='small' v-model={this.tableData[rowIndex].remarks} />
              },
            },
          },
        ]
      },
      /** 详情按钮操作 */
      handleDetails(row, type) {
        this.schemeDetails = row
        this.open = true
        this.type = type

        this.formTitle = type == 1 ? '调度方案详情' : '调度方案修改'
        getSchemaEvaluates({ schemaId: row.schemaId }).then(res => {
          let tmp = res.data
          this.schemeTabs = tmp.map(item => {
            return { ...item, name: '方案' + toChineseNum(item.type), id: item.type }
          })
          console.log('详情  获取评价123', this.schemeTabs)
          this.getDetails(row, this.schemeTabs[0]?.id)
        })
      },
      getDetails(row, type) {
        console.log('**** 获取详情 schemaId res 111', row, type)
        getSchema({ schemaId: row.schemaId, evaluateType: type }).then(res => {
          console.log('**** 获取详情 schemaId res 222', res)
          // this.canalTabs2 = res.data?.reduce((acc, item) => {
          //   // 对于每个元素，遍历其 projectWlvs 数组
          //   item.projectWlvs.forEach(projectWlv => {
          //     acc.push({
          //       chId: projectWlv.chId,
          //       chCode: projectWlv.chCode,
          //       chName: projectWlv.chName,
          //       projects: projectWlv.projects,
          //     })
          //   })

          //   return acc
          // }, [])
          // this.selectCanalTab2(0, this.canalTabs2[0])
          this.canalTabs = res.data.modelChannelResDurationVOList

          this.selectCanalTab(0, this.canalTabs[0])
          console.log('**** 获取canalTabs2 data res', res, this.canalTabs2)

          this.modelChannelResGateFlowVOList = res.data.modelChannelResGateFlowVOList || []
          this.modelChannelResGateWlVOList = res.data.modelChannelResGateWlVOList || []
          this.modelChannelResStakeWlVOList = res.data.modelChannelResStakeWlVOList || []

          console.log('**** 获取canalTabs2 data res', res, this.canalTabs, res.data.modelChannelResDurationVOList)
        })
      },

      /** 提交按钮 */
      submitForm() {
        this.loading = true
        setTimeout(() => {
          this.$message.success('提交成功')
          this.loading = false
          this.cancel()
        }, 1000)
      },

      showEvaluationModal() {
        this.showEvaluate = true
        this.$emit('showEvaluation', this.schemeDetails)
      },
    },
  }
</script>
<style lang="less" scoped>
  :deep(.vxe-table--header-inner-wrapper) {
    height: 40px !important;
  }
  .dispatch-content {
    display: flex;
    flex-direction: column;
    padding-top: 10px;
  }

  .basic-info {
    background-color: #f7f8fa;
    padding: 12px;
    margin-top: 10px;
  }

  .scheme-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e5e6eb;
    position: relative;
    margin-bottom: 10px;

    .scheme-tabs {
      display: flex;

      .scheme-tab {
        padding: 0 16px;
        cursor: pointer;
        color: #4a5568;
        height: 24px;
        line-height: 0px;
        position: relative;
        border-bottom: 2px solid transparent;
        margin-bottom: -20px;

        &:not(:last-child)::after {
          display: none;
        }

        &.active {
          color: #438efc;
          border-bottom: 2px solid #438efc;
          font-weight: 500;
          z-index: 1;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 5px;

      .action-btn {
        padding: 0 8px;
        height: 24px;
        line-height: 24px;
        color: #4a5568;
        cursor: pointer;
        border: 1px solid #f2f3f5;
        border-radius: 3px;
        text-align: center;
        min-width: 60px;
        font-size: 14px;
        white-space: nowrap;
        display: flex;
        justify-content: center;
        align-items: center;

        &.active {
          background-color: #438efc;
          color: white;
          border-color: #438efc;
        }
      }
    }
  }

  .canal-tabs-wrapper {
    margin-top: 10px;
    display: flex;
    align-items: center;
  }

  .canal-tabs-container {
    background-color: #f2f3f8;
    padding: 4px 6px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: auto;
  }

  .canal-tabs {
    display: flex;
    align-items: center;
  }

  .canal-tab {
    padding: 0 8px;
    cursor: pointer;
    margin-right: 8px;
    height: 22px;
    line-height: 22px;
    color: #4a5568;
    text-align: center;

    &.active {
      color: #438efc;
      font-weight: 500;
      background-color: white;
      border-radius: 2px;
    }
  }

  .flow-info {
    color: #f97316;
    font-size: 14px;
    margin-left: 10px;
    display: flex;
    align-items: center;
  }

  .content-container {
    margin-top: 10px;
  }

  .data-content {
    width: 100%;
  }

  .accordion {
    width: 100%;

    .accordion-item {
      border: 1px solid #f0f0f0;
      margin-bottom: 10px;
      background-color: white;
      border-radius: 2px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

      &.collapsed {
        background-color: #f7f8fa;
      }

      &.expanded {
        border-color: #e5e6eb;
      }

      .accordion-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        cursor: pointer;
        height: 46px;

        .accordion-title {
          display: flex;
          align-items: center;

          .anticon {
            margin-right: 8px;
            font-size: 12px;
          }

          span {
            font-weight: 500;
          }
        }

        .accordion-actions {
          display: flex;
          gap: 8px;

          .ant-btn-primary {
            background-color: #438efc;
            border-color: #438efc;
          }

          .ant-btn {
            padding: 0 8px;
            height: 24px;
            line-height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .accordion-content {
        padding: 0 16px 16px;
        position: relative;

        .flow-tag {
          position: absolute;
          right: 16px;
          top: 0;
          color: #f97316;
          font-size: 14px;
        }
      }
    }
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: #f5f5f5;
    padding: 8px;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 8px;
  }

  .type1 {
    background: url('~@/assets/images/dispatch/1.png') no-repeat;
    background-size: 100% 100%;
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: #f2f3f5;
    padding: 10px 4px;
    font-size: 13px;
    font-weight: 500;
  }

  :deep(.ant-table-bordered .ant-table-thead > tr > th) {
    border-right: 1px solid #f0f0f0;
  }

  :deep(.ant-table-bordered .ant-table-tbody > tr > td) {
    border-right: 1px solid #f0f0f0;
  }

  .tab-content {
    display: flex;
    width: 100%;
    height: 500px;
  }
</style>
