<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="560"
  >
    <div slot="content">
      <div class="table-panel">
        <a-form-model ref="form" :model="form" :rules="rules" layout="horizontal">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="路线名称" prop="lineName">
                <a-input v-model="form.lineName" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="备注">
                <a-textarea v-model="form.remarks" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addPatrolRoute, editPatrolRoute } from '../services.js'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormPatrolRoute',
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,

        formTitle: '',

        form: {
          lineName: undefined,
          remarks: undefined,
        },
        open: false,
        rules: {
          lineName: [{ required: true, message: '路线名称不能为空', trigger: 'blur' }],
        },
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('ok')
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handleAdd() {
        this.open = true
        this.formTitle = '新增'
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.form = record
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.form.lineId !== undefined) {
              editPatrolRoute(this.form)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            } else {
              addPatrolRoute(this.form)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped></style>
