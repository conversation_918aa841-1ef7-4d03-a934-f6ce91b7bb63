import request from '@/utils/request'

// 列表分页查询
export function getPatrolPlanList(data) {
  return request({
    url: '/patrol/reuse/plan/page',
    method: 'post',
    data,
  })
}

// 新增
export function addPatrolPlan(data) {
  return request({
    url: '/patrol/reuse/plan/add',
    method: 'post',
    data,
  })
}
//编辑
export function editPatrolPlan(data) {
  return request({
    url: '/patrol/reuse/plan/update',
    method: 'post',
    data,
  })
}

// 删除
export function deletePatrolPlan(params) {
  return request({
    url: '/patrol/reuse/plan/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 详情
export function getPatrolPlanDetails(params) {
  return request({
    url: '/patrol/reuse/plan/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 启用
export function startPatrolPlan(params) {
  return request({
    url: '/patrol/reuse/plan/start',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 禁用
export function stopPatrolPlan(params) {
  return request({
    url: '/patrol/reuse/plan/stop',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
